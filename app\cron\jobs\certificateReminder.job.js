// Packages
const { <PERSON>ron<PERSON><PERSON> } = require('cron');

// Services
const uploadCertificateService = require('../../services/upload-certificate.service');
const memberService = require('../../services/member.service');

// Utils
const commonFunctionsUtils = require('../../utils/common-function.utils');
const mailerUtils = require('../../utils/mailer.utils');
const constantsUtils = require('../../utils/constants.utils');

/**
 * Job to send certificate expiry reminders to users
 */
const certificateReminderJob = new CronJob(process.env.CERTIFICATE_CRON_SCHEDULE, async () => {
  console.log(constantsUtils.CERTIFICATE_REMAINDER_CRON_STARTED, new Date().toLocaleString());

  try {
    const currentDate = commonFunctionsUtils.getCurrentTimeInTimezone(
      global.constant.DEFAULT_TIMEZONE
    );
    const reminderWindows = global.constant.CERTIFICATE_EXPIRY;

    const certificates = await uploadCertificateService.getUserUploadCertificate({
      isActive: true,
      status: 'approved',
      internal: false,
      deletedAt: null,
    });

    for (const cert of certificates) {
      const { user, endDate, name } = cert;
      if (!endDate || !user?.isActive || !user.email) continue;

      const memberProjects = await memberService.getAllMember({
        user: user._id,
        deletedAt: null,
      });

      const isAssigned = memberProjects.some(m => m.project?.isActive);

      if (!isAssigned) continue;

      const expiryDate = commonFunctionsUtils.getCurrentTimeInTimezone(
        global.constant.DEFAULT_TIMEZONE,
        endDate
      );

      const daysRemaining = Math.floor((expiryDate - currentDate) / global.constant.DAY_CONVERTOR);
      const matchedWindow = reminderWindows.find(w => w.days === daysRemaining);

      if (!matchedWindow) continue;

      const commonTemplateData = {
        expiryDate: commonFunctionsUtils.formatDate(endDate),
        certificateName: name,
        daysAfterExpiry: daysRemaining < 0 ? `${Math.abs(daysRemaining)} day(s)` : '',
        daysRemaining: daysRemaining >= 0 ? `${daysRemaining} day(s)` : '',
        userFullName: `${user.callingName} ${user.lastName}`,
        email: user.email,
        supportTeamEmail: process.env.SUPPORT_TEAM_EMAIL,
        currentYear: new Date().getFullYear(),
        bestRegards: process.env.BEST_REGARDS,
        organizationName: process.env.ORGANIZATION_NAME,
      };

      const template =
        daysRemaining < 0
          ? process.env.SENDGRID_CERTIFICATE_ALERT_REMAINDER
          : process.env.SENDGRID_CERTIFICATE_EXPIRY_REMAINDER;

      const templateData = {
        ...commonTemplateData,
        logo:
          daysRemaining < 0
            ? global.constant.CERTIFICATE_EXPIRED_LOGO
            : global.constant.CERTIFICATE_ALERT_LOGO,
      };

      await mailerUtils.sendMailer(user.email.toString(), template, templateData);
    }

    console.log(constantsUtils.CERTIFICATE_REMAINDER_CRON_COMPLETED);
  } catch (error) {
    console.error(constantsUtils.ERROR_CERTIFICATE_REMAINDER_CRON, error);
  }
});

module.exports = certificateReminderJob;
