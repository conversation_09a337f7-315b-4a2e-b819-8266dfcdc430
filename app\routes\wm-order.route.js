// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// validator
const validator = require('../validators/wm-order.validator');

// controller
const wmOrderController = require('../controllers/wm-order.controller');

routes.get('/counter', verifyToken, authAccount, validate, wmOrderController.getWmEquipmentCounter);

routes.get(
  '/:equipmentTypeId/get-linked-equipments/:orderId',
  verifyToken,
  authAccount,
  validate,
  wmOrderController.getLinkedEquipments
);

routes.patch(
  '/update-linked-equipment',
  verifyToken,
  authAccount,
  validate,
  wmOrderController.updateLinkedEquipments
);

routes.patch(
  '/remove-linked-equipment/:equipmentId',
  verifyToken,
  authAccount,
  validate,
  wmOrderController.removeLinkedEquipment
);

routes.get('', verifyToken, authAccount, validate, wmOrderController.getPMOrders);

routes.get(
  '/return-order',
  verifyToken,
  authAccount,
  validate,
  wmOrderController.warehouseCheckinList
);
routes.get('/qr-code', verifyToken, authAccount, validate, wmOrderController.getEquipmentByQRCode);

routes.get(
  '/return/:returnOrderId',
  verifyToken,
  authAccount,
  validate,
  wmOrderController.warehouseOrderDetails
);

routes.get(
  '/:orderId',
  verifyToken,
  authAccount,
  validate,
  wmOrderController.getPMOrderDetailsByOrderId
);

routes.patch(
  '/approve-reject/:orderId',
  verifyToken,
  authAccount,
  validator.approveRejectPMOrderValidationRule(),
  validate,
  wmOrderController.approvedRejectPMOrder
);

routes.patch(
  '/link-equipment/:pmOrderManageId',
  verifyToken,
  authAccount,
  validator.linkEquipmentWithOrderValidationRule(),
  validate,
  wmOrderController.linkEquipmentWithOrder
);

routes.patch(
  '/reject/:pmOrderManageId',
  verifyToken,
  authAccount,
  validate,
  wmOrderController.rejectPMOrderManageEquipment
);

//todo
routes.patch(
  '/update-order-status/:pmOrder',
  verifyToken,
  authAccount,
  validator.updateOrderStatusValidationRule(),
  validate,
  wmOrderController.checkAndChangeOrderStatus
);

routes.get(
  '/liked-equipment/:pmOrderManageId',
  verifyToken,
  authAccount,
  validate,
  wmOrderController.getLinkedEquipmentByOrderDetailId
);

routes.get(
  '/get-unlink-equipment/:orderId/:qrCode',
  verifyToken,
  authAccount,
  validate,
  wmOrderController.getUnlinkEquipment
);

routes.get(
  '/:orderId/:qrCode',
  verifyToken,
  authAccount,
  validate,
  wmOrderController.getEquipmentDataByQR
);

routes.patch('/check-in', verifyToken, authAccount, validate, wmOrderController.warehouseCheckIn);

routes.patch(
  '/reject-partial-order/:orderId',
  verifyToken,
  authAccount,
  validator.validateParamIds(),
  validate,
  wmOrderController.rejectPartialOrder
);

routes.get(
  '/order/:orderId/export-pdf',
  verifyToken,
  authAccount,
  validate,
  wmOrderController.getOrderPDFDetails
);

routes.patch(
  '/remove-quantity-from-open-order/:orderId',
  verifyToken,
  authAccount,
  validate,
  wmOrderController.removeQuantityFromOpenOrder
);

module.exports = routes;
