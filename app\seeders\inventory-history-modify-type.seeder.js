const InventoryHistory = require('../models/inventory-history.model');

exports.up = async () => {
  try {
    const result = await InventoryHistory.updateMany(
      { type: 'maintenance' },
      { type: 'quarantine' }
    );

    console.log('Updated Documents', result.modifiedCount);
  } catch (error) {
    console.error('Error ', error);
  }
};

exports.down = async () => {
  try {
    const result = await InventoryHistory.updateMany(
      { type: 'quarantine' },
      { type: 'maintenance' }
    );

    console.log('Updated Documents', result.modifiedCount);
  } catch (error) {
    console.error('Error ', error);
  }
};
