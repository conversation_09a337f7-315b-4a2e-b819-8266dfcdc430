const mongoose = require('mongoose');
// Services
const wmOrderService = require('../services/wm-order.service');
const pmOrderService = require('../services/pm-order.service');
const pmOrderManageEquipmentService = require('../services/pm-order-manage-equipment.service');
const equipmentService = require('../services/equipment.service');
const equipmentOrderHistoryService = require('../services/equipment-order-history.service');
const inventoryHistoryService = require('../services/inventory-history.service');
const warehouseOrderService = require('../services/wm-order.service');
const returnOrderHistoryService = require('../services/return-order-history.service');
const returnOrderService = require('../services/return-order.service');
const projectService = require('../services/project.service');
const pdfTemplateService = require('../services/pdf-template.service');
const currencyUnitService = require('../services/currency-unit.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const commonUtils = require('../utils/common.utils');
const responseUtils = require('../utils/response.utils');
const { validateSearch } = require('../utils/common-function.utils');
const { transactionOptions, orderStatusArray } = require('../utils/json-format.utils');
const mailerUtils = require('../utils/mailer.utils');
const HTTP_STATUS = require('../utils/status-codes');

/**
 * Get PM Orders
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getPMOrders = async (req, res) => {
  try {
    let page = req.query.page ? req.query.page : 0;
    let perPage = req.query.perPage ? req.query.perPage : 100;
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;
    let search = await validateSearch(req.query.search);

    if (!search && search !== '') {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.INVALID_SEARCH));
    }
    let filterData = {
      account: req.userData.account,
      deletedAt: null,
      ...(req.query.status ? { status: req.query.status } : { status: { $ne: 'open' } }),
    };

    // Add project filter if project status is provided
    if ('projectStatus' in req.query) {
      filterData = await commonUtils.filterProjectStatus(
        req.query.projectStatus,
        req.userData.account,
        filterData
      );
    }

    delete filterData?.projectStatus;

    if (
      req?.assignedProjectList &&
      !orderStatusArray.includes(req.query.status) &&
      ![global.constant.ADMIN_ROLE, global.constant.SUPER_ADMIN_ROLE].includes(
        req.userData.role.title
      )
    ) {
      filterData.project = { $in: req.assignedProjectList };
    }

    if (
      req?.assignedProjectList === undefined &&
      !orderStatusArray.includes(req.query.status) &&
      ![global.constant.ADMIN_ROLE, global.constant.SUPER_ADMIN_ROLE].includes(
        req.userData.role.title
      )
    ) {
      return res
        .status(200)
        .json(responseUtils.successResponse(constantUtils.GET_PM_ORDER_REQUEST, []));
    }

    const pmOrders = await wmOrderService.getPMOrderList(filterData, page, perPage, sort, search);

    // Remove null data
    for (let pmOrder of pmOrders) {
      pmOrder.equipmentTypeData = pmOrder.equipmentTypeData.filter(typeData => typeData !== null);
    }

    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.GET_PM_ORDER_REQUEST, pmOrders));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get PM Order Details by Order Id
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getPMOrderDetailsByOrderId = async (req, res) => {
  try {
    let { orderId } = req.params;
    let page = req.query.page ? req.query.page : 0;
    let perPage = req.query.perPage ? req.query.perPage : 100;
    let filterData = {
      _id: orderId,
      account: req.userData.account,
      deletedAt: null,
    };

    const exist = await pmOrderService.searchPMOrder(filterData);

    if (!exist) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.PM_ORDER_REQUEST_NOT_FOUND));
    }

    let sort =
      req.query.sort && req.query.sort === 'asc'
        ? { 'equipmentTypeData.createdAt': 1 }
        : { 'equipmentTypeData.createdAt': -1 };

    if ('sortByType' in req.query) {
      sort =
        req.query.sortByType === 'asc'
          ? { 'equipmentTypeDetails.type': 1 }
          : { 'equipmentTypeDetails.type': -1 };
    }

    let search = await validateSearch(req.query.search);

    if (!search && search !== '') {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.INVALID_SEARCH));
    }

    delete filterData._id;
    filterData.pmOrder = commonUtils.toObjectId(orderId);

    if (req.query.status !== undefined) {
      filterData.status = req.query.status;
    }
    const pmOrders = await wmOrderService.getPMOrderManageDataByOrderId(
      filterData,
      page,
      perPage,
      sort,
      search
    );

    // Remove null data
    for (let pmOrder of pmOrders) {
      pmOrder.equipmentTypeData = pmOrder.equipmentTypeData.filter(typeData => typeData !== null);
    }

    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.GET_PM_ORDER_REQUEST, pmOrders));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Approve or Reject PM Order
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.approvedRejectPMOrder = async (req, res) => {
  try {
    let { orderId } = req.params;
    let {
      status,
      pmOrderManage,
      username,
      wmApprovedQuantity,
      pmRequestedQuantity,
      orderNumber,
      companyName,
      project,
      createdByEmail,
    } = req.body;

    await mailerUtils.sendMailer(
      createdByEmail,
      process.env.SENDGRID_APPROVE_REJECT_ORDER_REQUEST_EMAIL,
      {
        username: await commonUtils.alterStringFromRequestString(username),
        pmRequestedQuantity,
        wmApprovedQuantity,
        orderNumber,
        status: await commonUtils.alterStringFromRequestString(status),
        companyName,
        project,
        createdByEmail,
        orderLink: `${process.env.BASE_URL}/client/project-orders`,
        ...global.constant.EMAIL_CONSTANTS,
      }
    );
    const exist = await pmOrderService.searchPMOrder({
      _id: orderId,
      account: req.userData.account,
      deletedAt: null,
    });

    if (!exist) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.PM_ORDER_REQUEST_NOT_FOUND));
    }

    await pmOrderService.updatePMOrder(orderId, {
      status,
      updatedBy: req.userData._id,
    });

    for (let key in pmOrderManage) {
      const manageId = pmOrderManage[key].id;
      delete pmOrderManage[key].id;
      pmOrderManage[key].status = status;
      await pmOrderManageEquipmentService.updatePMOrderManageEquipment(
        manageId,
        pmOrderManage[key]
      );
    }

    res.status(200).json(responseUtils.successResponse(constantUtils.UPDATE_PM_ORDER_REQUEST));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Link Equipment with PM Order
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.linkEquipmentWithOrder = async (req, res) => {
  const session = await mongoose.startSession();
  const transactionOptions = {
    readConcern: { level: 'snapshot' },
    writeConcern: { w: 'majority' },
    readPreference: 'primary',
  };
  try {
    let { pmOrderManageId } = req.params;
    let { equipmentId, quantity, ...restBody } = req.body;

    if (!Number.isInteger(quantity) || quantity < 1) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.INVALID_QUANTITY));
    }

    session.startTransaction(transactionOptions);

    let exist = await pmOrderManageEquipmentService.searchPMOrderManageEquipment({
      _id: pmOrderManageId,
      status: { $nin: ['rejected'] },
      account: req.userData.account,
      deletedAt: null,
    });

    if (!exist) {
      await session.abortTransaction();
      session.endSession();
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.PM_ORDER_DETAILS_NOT_FOUND));
    }

    const getEquipment = await equipmentService.getSingleEquipmentByFilter({
      _id: equipmentId,
      equipmentType: exist.equipmentType,
    });

    if (!getEquipment) {
      await session.abortTransaction();
      session.endSession();
      return res.status(404).json(responseUtils.errorResponse(constantUtils.EQUIPMENT_NOT_FOUND));
    }

    if (getEquipment.quantity < quantity) {
      await session.abortTransaction();
      session.endSession();
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.EQUIPMENT_OUT_OF_STOCK));
    }

    const searchEquipmentInOrder =
      await equipmentOrderHistoryService.getEquipmentOrderHistoryByFilter({
        pmOrder: exist.pmOrder,
        equipmentType: exist.equipmentType,
        pmOrderManageEquipment: exist._id,
        account: req.userData.account,
        deletedAt: null,
      });

    let totalQuantity = 0;

    for (const element of searchEquipmentInOrder) {
      totalQuantity += element.wmDispatchQuantity;
    }

    totalQuantity += quantity;

    if (totalQuantity > exist.wmApprovedQuantity) {
      await session.abortTransaction();
      session.endSession();
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.ASSING_EQUIPMENT_QUANTITY_MORE));
    }

    let linkedEquipment = exist.equipment;
    if (!exist.equipment.includes(getEquipment._id)) {
      linkedEquipment.push(getEquipment._id);
    }

    const updateOrderDetail = await pmOrderManageEquipmentService.updatePMOrderManageEquipment(
      pmOrderManageId,
      {
        equipment: linkedEquipment,
        updatedBy: req.userData._id,
        updatedAt: new Date(),
      },
      session
    );

    if (updateOrderDetail) {
      let { subStatus, wmDispatchReasonStatus, wmDispatchReason } = restBody;

      let historyFilter = {
        pmOrder: exist.pmOrder,
        status: 'pre-linked',
        equipmentType: exist.equipmentType,
        equipment: getEquipment._id,
        pmOrderManageEquipment: exist._id,
        account: req.userData.account,
        deletedAt: null,
      };

      const searchEquipmentHistory =
        await equipmentOrderHistoryService.getEquipmentOrderHistoryOneByFilter(historyFilter);

      if (searchEquipmentHistory) {
        await equipmentOrderHistoryService.updateEquipmentHistory(historyFilter, {
          wmDispatchQuantity: searchEquipmentHistory.wmDispatchQuantity + quantity,
          updatedBy: req.userData._id,
          updatedAt: new Date(),
        });
      } else {
        // create equipment order history
        await equipmentOrderHistoryService.createEquipmentOrderHistory(
          {
            pmOrder: exist.pmOrder,
            equipment: getEquipment._id,
            equipmentType: exist.equipmentType,
            pmOrderManageEquipment: exist._id,
            status: 'pre-linked',
            wmDispatchQuantity: quantity,
            subStatus,
            wmDispatchReasonStatus,
            wmDispatchReason,
            account: req.userData.account,
            createdBy: req.userData._id,
            createdAt: new Date(),
          },
          { session }
        );
      }

      // create inventory history
      const getPMOrder = await pmOrderService.getPMOrderById(exist.pmOrder);

      await inventoryHistoryService.prepareInventoryHistoryAndCreate(
        getEquipment._id,
        'linked',
        'order',
        `${getEquipment.warehouse.name} -> ${getPMOrder.project.title}`,
        getPMOrder.orderNumber,
        quantity,
        '',
        exist.pmOrder,
        req.userData.account,
        req.userData._id,
        session
      );

      // update equipment quantity
      let remainingQuantity = getEquipment.quantity - quantity;
      await wmOrderService.updateEquipmentOnLinkEquipment(
        getEquipment._id,
        {
          quantity: remainingQuantity,
          updatedBy: req.userData._id,
          updatedAt: new Date(),
        },
        session
      );
    }
    await session.commitTransaction();
    session.endSession();

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.EQUIPMENT_LINKED_SUCCESSFULLY));
  } catch (error) {
    await session.abortTransaction();
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Reject PM Order Manage
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.rejectPMOrderManageEquipment = async (req, res) => {
  try {
    const { pmOrderManageId } = req.params;
    const { remark } = req.body;

    const exists = await pmOrderManageEquipmentService.searchPMOrderManageEquipment({
      _id: pmOrderManageId,
      account: req.userData.account,
      deletedAt: null,
    });
    if (!exists) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.PM_ORDER_DETAILS_NOT_FOUND));
    }

    const pmOrderManageData = await pmOrderManageEquipmentService.updatePMOrderManageEquipment(
      pmOrderManageId,
      {
        status: 'rejected',
        updatedBy: req.userData._id,
        updatedAt: new Date(),
        ...{ remark },
      }
    );

    if (pmOrderManageData) {
      let getPMOrderDetails = await pmOrderManageEquipmentService.getPMOrderManageEquipment({
        pmOrder: pmOrderManageData.pmOrder,
        account: req.userData.account,
        deletedAt: null,
      });

      let count = 0;
      for (let key in getPMOrderDetails) {
        if (getPMOrderDetails[key].status === 'rejected') {
          count++;
        }
      }

      if (getPMOrderDetails.length === count) {
        await pmOrderService.updatePMOrder(pmOrderManageData.pmOrder, {
          status: 'rejected',
          updatedBy: req.userData._id,
          updatedAt: new Date(),
        });
      }
    }

    res
      .status(200)
      .json(
        responseUtils.successResponse(constantUtils.PM_ORDER_DETAILS_REJECTED, pmOrderManageData)
      );
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Check And Change Order Status
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.checkAndChangeOrderStatus = async (req, res) => {
  try {
    const { pmOrder } = req.params;
    const { status, checkoutStatus, checkoutReasonStatus, checkoutReason } = req.body;

    const exists = await pmOrderService.getPMOrderById(pmOrder);
    if (!exists) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.PM_ORDER_REQUEST_NOT_FOUND));
    }

    let getPMOrderById = await pmOrderService.getPMOrderById(pmOrder);
    let statusPMOrder = {
      'partially-pre-transit': 'partially-in-transit',
      'partially-in-transit': 'partially-check-in',
    };

    const updateOrderStatus =
      ['partially-pre-transit'].includes(getPMOrderById.status) && status === 'check-in'
        ? 'partially-in-transit'
        : getPMOrderById.status;
    let newPMOrderStatus = statusPMOrder[updateOrderStatus] ?? status;

    let updateOrder = null;
    if (status === 'pre-transit') {
      let checkLinkedEquipmentInOrder =
        await equipmentOrderHistoryService.getEquipmentOrderHistoryOneByFilter({
          pmOrder,
          status: 'pre-linked',
          account: req.userData.account,
          deletedAt: null,
        });

      if (!checkLinkedEquipmentInOrder) {
        return res
          .status(400)
          .json(responseUtils.errorResponse(constantUtils.NEW_EQUIPMENT_NOT_LINKED_IN_ORDER));
      }
      let getProcessed = await wmOrderService.checkLinkedEquipment(pmOrder, req.userData._id);
      if (getProcessed && getProcessed > 0) {
        let newStatus = status;

        let getPMOrderDetails = await pmOrderManageEquipmentService.getPMOrderManageEquipment({
          pmOrder,
          account: req.userData.account,
          deletedAt: null,
        });

        for (let key of getPMOrderDetails) {
          if (
            [
              'partially-pre-transit',
              'partially-in-transit',
              'partially-check-in',
              'approved',
            ].includes(key.status)
          ) {
            newStatus = 'partially-pre-transit';
            break;
          }
        }
        updateOrder = await pmOrderService.updatePMOrder(pmOrder, {
          status: newStatus,
          updatedBy: req.userData._id,
          updatedAt: new Date(),
        });

        // Update Equipment Order History and Inventory History
        if (updateOrder) {
          let getEquipmentHistory =
            await equipmentOrderHistoryService.getEquipmentOrderHistoryByFilter({
              pmOrder,
              status: 'pre-linked',
              account: req.userData.account,
              deletedAt: null,
            });

          await equipmentOrderHistoryService.updateEquipmentHistoryByFilter(
            {
              pmOrder: pmOrder,
              status: 'pre-linked',
              deletedAt: null,
            },
            {
              status: 'linked',
              updatedBy: req.userData._id,
              updatedAt: new Date(),
            }
          );

          let projectName = await projectService.getProjectById(
            updateOrder.project,
            req.userData.account
          );

          for (let data of getEquipmentHistory) {
            const getEquipment = await equipmentService.getSingleEquipmentByFilter({
              _id: data.equipment,
              equipmentType: data.equipmentType,
            });

            //update Inventory History
            await inventoryHistoryService.prepareInventoryHistoryAndCreate(
              data.equipment,
              'pre-transit',
              'order',
              `${getEquipment.warehouse.name} -> ${projectName.title}`,
              exists.orderNumber,
              data.wmDispatchQuantity,
              '',
              exists._id,
              req.userData.account,
              req.userData._id
            );
          }
        }
      }
    } else {
      updateOrder = await pmOrderService.updatePMOrder(pmOrder, {
        status: newPMOrderStatus,
        updatedBy: req.userData._id,
        updatedAt: new Date(),
      });
    }

    if (updateOrder && status !== 'pre-transit') {
      let manageOrdStatus = {
        'pre-transit': 'in-transit',
        'partially-pre-transit': 'partially-in-transit',
        'partially-in-transit': 'partially-check-in',
        'in-transit': 'check-in',
      };
      if (['check-in'].includes(status)) {
        let getPMOrderDetails = await pmOrderManageEquipmentService.getPMOrderManageEquipment({
          pmOrder,
          status: {
            $in: ['pre-transit', 'in-transit', 'partially-in-transit', 'partially-pre-transit'],
          },
          account: req.userData.account,
          deletedAt: null,
        });

        for (let key in getPMOrderDetails) {
          let getEquipmentHistory =
            await equipmentOrderHistoryService.getEquipmentOrderHistoryByFilter({
              pmOrder,
              pmOrderManageEquipment: getPMOrderDetails[key]._id,
              equipmentType: getPMOrderDetails[key].equipmentType,
              status: { $in: ['in-transit', 'pre-check-in'] },
              account: req.userData.account,
              deletedAt: null,
            });

          for (let historyKey in getEquipmentHistory) {
            const updateEquipHistory =
              await equipmentOrderHistoryService.updateEquipmentOrderHistory(
                getEquipmentHistory[historyKey]._id,
                {
                  status: 'check-in',
                  pmReceivedQuantity: getEquipmentHistory[historyKey]['wmDispatchQuantity'],
                  updatedBy: req.userData._id,
                  updatedAt: new Date(),
                }
              );

            let projectName = await projectService.getProjectById(
              updateOrder.project,
              req.userData.account
            );
            await inventoryHistoryService.prepareInventoryHistoryAndCreate(
              getEquipmentHistory[historyKey].equipment,
              'on-site',
              'order-received',
              projectName.title,
              exists.orderNumber,
              updateEquipHistory['pmReceivedQuantity'],
              '',
              exists._id,
              req.userData.account,
              req.userData._id
            );

            if (updateEquipHistory) {
              await pmOrderManageEquipmentService.updatePMOrderManageEquipment(
                updateEquipHistory.pmOrderManageEquipment,
                {
                  status:
                    getPMOrderDetails[key].status === 'partially-pre-transit'
                      ? 'partially-pre-transit'
                      : manageOrdStatus[getPMOrderDetails[key].status],
                  $inc: { pmReceivedQuantity: updateEquipHistory.wmDispatchQuantity },
                  updatedBy: req.userData._id,
                  updatedAt: new Date(),
                }
              );
            }
          }
        }
      } else {
        let projectName = await projectService.getProjectById(
          updateOrder.project,
          req.userData.account
        );

        let getPMOrderDetails = await pmOrderManageEquipmentService.getPMOrderManageEquipment({
          pmOrder,
          status: { $in: ['pre-transit', 'partially-pre-transit'] },
          account: req.userData.account,
          deletedAt: null,
        });
        for (let key of getPMOrderDetails) {
          let newManageStatus = manageOrdStatus[key.status];
          let updateManageData = await pmOrderManageEquipmentService.updatePMOrderManageEquipment(
            key._id,
            {
              ...(checkoutStatus && { checkoutStatus: checkoutStatus }),
              ...(checkoutReasonStatus && { checkoutReasonStatus: checkoutReasonStatus }),
              ...(checkoutReason && { checkoutReason: checkoutReason }),
              status: newManageStatus,
              updatedBy: req.userData._id,
              updatedAt: new Date(),
            }
          );

          for (let data of updateManageData.equipment) {
            let getEquipHistoryData =
              await equipmentOrderHistoryService.getEquipmentOrderHistoryByFilter({
                equipment: data,
                pmOrderManageEquipment: key._id,
                status: 'linked',
                deletedAt: null,
              });

            let quantity = 0;
            for (let equipHistoryData of getEquipHistoryData) {
              //update Equipment Order History
              const updatedEquipHistory =
                await equipmentOrderHistoryService.updateEquipmentOrderHistory(
                  equipHistoryData._id,
                  {
                    status: 'in-transit',
                    updatedBy: req.userData._id,
                    updatedAt: new Date(),
                  }
                );
              quantity += updatedEquipHistory.wmDispatchQuantity;

              //update Inventory History
              await inventoryHistoryService.prepareInventoryHistoryAndCreate(
                data,
                'in-transit',
                'order',
                projectName.title,
                exists.orderNumber,
                quantity,
                '',
                exists._id,
                req.userData.account,
                req.userData._id
              );
            }
          }
        }
      }
    }

    res.status(200).json(responseUtils.successResponse(constantUtils.UPDATE_PM_ORDER_DETAILS));
  } catch (error) {
    const status = error.statusCode ?? 500;
    res.status(status).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get Linked Equipment By Order Detail Id
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getLinkedEquipmentByOrderDetailId = async (req, res) => {
  try {
    const { pmOrderManageId } = req.params;

    const exists = await pmOrderManageEquipmentService.searchPMOrderManageEquipment({
      _id: pmOrderManageId,
      account: req.userData.account,
      deletedAt: null,
    });
    if (!exists) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.PM_ORDER_DETAILS_NOT_FOUND));
    }

    let filterData = {
      _id: commonUtils.toObjectId(pmOrderManageId),
    };

    const linkedEquipment = await wmOrderService.getLinkedEquipmentByOrderDetailByFilter(
      filterData
    );
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.GET_LINKED_EQUIPMENT, linkedEquipment));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get Equipment Data By QR
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getEquipmentDataByQR = async (req, res) => {
  try {
    const { orderId, qrCode } = req.params;

    const getOrderManageData = await pmOrderManageEquipmentService.getPMOrderManageEquipment({
      pmOrder: orderId,
      account: req.userData.account,
      deletedAt: null,
    });
    if (!getOrderManageData) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.PM_ORDER_DETAILS_NOT_FOUND));
    }

    let equipmentTypes = getOrderManageData.map(data => data.equipmentType);

    const getEquipment = await equipmentService.findQRCode(qrCode, { $in: equipmentTypes });

    if (getEquipment.length === 0) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.SCANNED_QR_NOT_IN_ORDER));
    }

    let filterOrderDetail = getOrderManageData.filter(data =>
      data.equipmentType.equals(getEquipment[0].equipmentType._id)
    );

    // Get equipment order history data
    const getOrderHistory = await equipmentOrderHistoryService.getEquipmentOrderHistoryByFilter({
      pmOrder: orderId,
      equipmentType: getEquipment[0].equipmentType._id,
      status: 'pre-linked',
      account: req.userData.account,
      deletedAt: null,
    });

    // calculate remaining approved quantity
    let remainingApprovedQuantity =
      filterOrderDetail[0].wmApprovedQuantity - filterOrderDetail[0].wmDispatchQuantity;

    // count linked equipment
    let countLinkedEquipment = 0;
    for (let data of getOrderHistory) {
      countLinkedEquipment += data.wmDispatchQuantity;
    }

    // check linked equipment is greater than 0 and calculate remaining
    remainingApprovedQuantity =
      countLinkedEquipment > 0
        ? filterOrderDetail[0].wmApprovedQuantity - countLinkedEquipment
        : remainingApprovedQuantity;

    // Get Inventory history id for unlink equipment from orders
    let inventoryHistory = await inventoryHistoryService.getInventoryHistoryOneByFilter({
      equipment: getEquipment[0]._id,
      account: req.userData.account,
      deletedAt: null,
      pmOrder: orderId,
    });

    const pmOrder = await pmOrderService.getPMOrderById(orderId); // Get PM Order details

    const pmOrderManageData =
      await pmOrderManageEquipmentService.getSinglePMOrderManageEquipmentByFilter({
        pmOrder: orderId,
        equipmentType: getEquipment[0].equipmentType._id,
        account: req.userData.account,
        deletedAt: null,
      });

    let openQuantity =
      pmOrderManageData.wmApprovedQuantity -
        (pmOrderManageData.wmDispatchQuantity + countLinkedEquipment) || 0;

    let returnDetails = {
      equipmentDetails: {
        equipmentId: getEquipment[0]._id,
        equipmentOrderHistoryId: getOrderHistory[0]?._id, // Get equipment order history id for unlink equipment from orders
        inventoryHistoryId: inventoryHistory?._id, // Get Inventory history id for unlink equipment from orders
        equipmentName: getEquipment[0].name,
        equipmentNumber: getEquipment[0].equipmentNumber,
        serialNumber: getEquipment[0].serialNumber,
        equipmentTypeId: getEquipment[0].equipmentType._id,
        equipmentTypeName: getEquipment[0].equipmentType.type,
        equipmentTypePrice: getEquipment[0].equipmentType.price,
        equipmentQuantityType: getEquipment[0].equipmentType.quantityType.quantityType,
        equipmentPriceType: getEquipment[0].equipmentType.quantityType.priceType,
        quantity: getEquipment[0].quantity,
        equipmentImage: getEquipment[0].equipmentImage,
        certificate: getEquipment[0].certificateType,
        qrCode: getEquipment[0].qrCode[getEquipment[0].qrCode.length - 1].code,
      },
      orderDetail: {
        wmApprovedQuantity: filterOrderDetail[0].wmApprovedQuantity,
        linkedQuantity: getOrderHistory[0]?.wmDispatchQuantity,
        remainingApprovedQuantity,
        manageOrderId: filterOrderDetail[0]._id,
        orderId: filterOrderDetail[0].pmOrder,
        orderNumber: pmOrder.orderNumber,
        createdDate: pmOrder.createdAt,
        requestedQuantity: pmOrderManageData.wmApprovedQuantity,
        packedQuantity: countLinkedEquipment,
        openQuantity,
      },
    };

    res.status(200).json(responseUtils.successResponse(constantUtils.GET_EQUIPMENT, returnDetails));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get Unlink Equipment Scan QR
 *
 * @param {*} req
 * @param {*} res
 */
exports.getUnlinkEquipment = async (req, res) => {
  try {
    const { orderId, qrCode } = req.params;

    const getEquipment = await equipmentService.findQRCode(qrCode);

    if (!getEquipment.length) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.SCANNED_QR_NOT_IN_ORDER));
    }

    const getOrderHistory = await equipmentOrderHistoryService.getEquipmentOrderHistoryByFilter({
      pmOrder: orderId,
      equipment: getEquipment[0]._id,
      equipmentType: getEquipment[0].equipmentType._id,
      status: 'pre-linked',
      account: req.userData.account,
      deletedAt: null,
    });

    if (!getOrderHistory.length) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.REQUIRED_EQUIPMENT_NOT_LINK));
    }

    let inventoryHistory = await inventoryHistoryService.getInventoryHistoryOneByFilter({
      equipment: getEquipment[0]._id,
      account: req.userData.account,
      deletedAt: null,
      pmOrder: orderId,
    });

    const pmOrderManageEquipment = await pmOrderManageEquipmentService.searchPMOrderManageEquipment(
      {
        pmOrder: orderId,
        equipment: getEquipment[0]._id,
        equipmentType: getEquipment[0].equipmentType._id,
        account: req.userData.account,
        deletedAt: null,
      }
    );

    if (!inventoryHistory) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.SCANNED_QR_NOT_IN_ORDER));
    }

    let returnDetails = {
      equipmentDetails: {
        equipmentId: getEquipment[0]._id,
        equipmentOrderHistoryId: getOrderHistory[0]?._id, // Get equipment order history id for unlink equipment from orders
        inventoryHistoryId: inventoryHistory?._id, // Get Inventory history id for unlink equipment from orders
        pmOrderManageEquipment: pmOrderManageEquipment?._id,
        equipmentName: getEquipment[0].name,
        equipmentNumber: getEquipment[0].equipmentNumber,
        serialNumber: getEquipment[0].serialNumber,
        equipmentTypeId: getEquipment[0].equipmentType._id,
        equipmentTypeName: getEquipment[0].equipmentType.type,
        equipmentTypePrice: getEquipment[0].equipmentType.price,
        equipmentQuantityType: getEquipment[0].equipmentType.quantityType.quantityType,
        equipmentPriceType: getEquipment[0].equipmentType.quantityType.priceType,
        quantity: getEquipment[0].quantity,
        equipmentImage: getEquipment[0].equipmentImage,
        linkedQuantity: getOrderHistory[0]?.wmDispatchQuantity,
      },
    };
    res.status(200).json(responseUtils.successResponse(constantUtils.GET_EQUIPMENT, returnDetails));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get Warehouse Checkin List
 *
 * @param {*} req
 * @param {*} res
 */
exports.warehouseCheckinList = async (req, res) => {
  try {
    let filter = {
      account: req.userData.account,
      deletedAt: null,
      status: req.query.status,
    };

    // Add project filter if project status is provided
    if ('projectStatus' in req.query) {
      filter = await commonUtils.filterProjectStatus(
        req.query.projectStatus,
        req.userData.account,
        filter
      );
    }

    delete filter?.projectStatus;

    let response = await warehouseOrderService.warehouseCheckinList(filter);

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.CHECKIN_REQUEST_LIST, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get Warehouse return Details
 *
 * @param {*} req
 * @param {*} res
 */
exports.warehouseOrderDetails = async (req, res) => {
  try {
    let page = req.query.page || 0;
    let perPage = req.query.perPage || 10;
    let status = req.query.status || '';
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;

    let filter = {
      account: req.userData.account,
      deletedAt: null,
      returnOrder: commonUtils.toObjectId(req.params.returnOrderId),
    };

    let response = await warehouseOrderService.warehouseOrderDetails(
      filter,
      page,
      perPage,
      status,
      sort
    );

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.CHECKIN_REQUEST_LIST, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Warehouse CheckIn
 *
 * @param {*} req
 * @param {*} res
 */
exports.warehouseCheckIn = async (req, res) => {
  const session = await mongoose.startSession();
  const transactionOption = { ...transactionOptions };

  try {
    session.startTransaction(transactionOption);
    const { orderId, equipment, wmRemark, wmComment, checkinReasonStatus, checkinReason } =
      req.body;

    if (
      !orderId ||
      !equipment ||
      !mongoose.Types.ObjectId.isValid(orderId) ||
      !mongoose.Types.ObjectId.isValid(equipment)
    ) {
      await session.abortTransaction();
      session.endSession();
      return res.status(400).json(responseUtils.errorResponse(constantUtils.MISSING_FIELDS));
    }

    const commonFilter = { account: req.userData.account, deletedAt: null };

    if (!orderId || !equipment) {
      await session.abortTransaction();
      session.endSession();

      return res.status(400).json(responseUtils.errorResponse(constantUtils.MISSING_FIELDS));
    }

    const updateOrderReturn = await returnOrderHistoryService.updateReturnOrder(
      {
        ...commonFilter,
        equipment: commonUtils.toObjectId(equipment),
        returnOrder: commonUtils.toObjectId(orderId),
      },
      {
        status: 'in-stock',
        subStatus: req.body.subStatus,
        wmReceivedQuantity: req.body.wmReceivedQuantity,
        wmComment,
        checkinReasonStatus,
        checkinReason,
      },
      session
    );

    if (!updateOrderReturn) {
      await session.abortTransaction();
      session.endSession();
      return res.status(400).json(responseUtils.errorResponse(constantUtils.FAILED_TO_PROCESS));
    }

    const equipmentHistoryResponse = await equipmentOrderHistoryService.getEquipmentHistory({
      ...commonFilter,
      equipment: commonUtils.toObjectId(equipment),
      returnOrder: {
        $elemMatch: {
          returnOrder: commonUtils.toObjectId(orderId),
        },
      },
    });

    if (!equipmentHistoryResponse) {
      await session.abortTransaction();
      session.endSession();
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.NO_EQUIPMENT_ORDER_HISTORY));
    }

    let wmQuantity;
    if (equipmentHistoryResponse.wmReceivedQuantity) {
      wmQuantity =
        parseInt(equipmentHistoryResponse.wmReceivedQuantity) +
        parseInt(req.body.wmReceivedQuantity);
    } else {
      wmQuantity = parseInt(req.body.wmReceivedQuantity);
    }

    const equipmentHistory = await equipmentOrderHistoryService.updateEquipmentHistoryByFilter(
      {
        _id: equipmentHistoryResponse._id,
      },
      {
        status:
          equipmentHistoryResponse.pmReceivedQuantity === wmQuantity
            ? 'in-stock'
            : 'partially-in-stock',
        wmReceivedQuantity: wmQuantity,
        checkinReasonStatus,
        checkinReason,
      },
      session
    );
    if (!equipmentHistory) {
      await session.abortTransaction();
      session.endSession();
      return res.status(400).json(responseUtils.errorResponse(constantUtils.FAILED_TO_PROCESS));
    }

    const [equipmentHistoryData] =
      await equipmentOrderHistoryService.getEquipmentOrderHistoryByFilter({
        ...commonFilter,
        equipment: commonUtils.toObjectId(equipment),
      });

    if (!equipmentHistory) {
      await session.abortTransaction();
      session.endSession();
      return res.status(400).json(responseUtils.errorResponse(constantUtils.FAILED_TO_PROCESS));
    }

    const equipmentData = await equipmentService.getSingleEquipmentByFilter({ _id: equipment });

    if (!equipmentData) {
      await session.abortTransaction();
      session.endSession();
      return res.status(400).json(responseUtils.errorResponse(constantUtils.FAILED_TO_PROCESS));
    }

    const updateEquipment = await equipmentService.updateEquipment(
      commonUtils.toObjectId(equipment),
      {
        quantity: parseInt(equipmentData.quantity) + parseInt(req.body.wmReceivedQuantity),
      },
      session
    );

    if (!updateEquipment) {
      await session.abortTransaction();
      session.endSession();
      return res.status(400).json(responseUtils.errorResponse(constantUtils.FAILED_TO_PROCESS));
    }
    const orderNumber = await returnOrderService.getSingleReturnOrderDataByFilter({ _id: orderId });

    const updateInventory = await inventoryHistoryService.prepareInventoryHistoryAndCreate(
      commonUtils.toObjectId(equipment),
      'in-stock',
      'return-received',
      equipmentData.warehouse.name,
      orderNumber.orderNumber,
      parseInt(req.body.wmReceivedQuantity),
      '',
      equipmentHistoryData.pmOrder,
      req.userData.account,
      req.userData._id,
      session
    );

    if (!updateInventory) {
      await session.abortTransaction();
      session.endSession();
      return res.status(400).json(responseUtils.errorResponse(constantUtils.FAILED_TO_PROCESS));
    }
    let retrunOrderHistoryData = await returnOrderHistoryService.getReturnOrderHistory({
      account: req.userData.account,
      returnOrder: orderId,
      deletedAt: null,
    });

    let allCheckin = retrunOrderHistoryData.every(item => item.status === 'in-stock');
    if (allCheckin) {
      const updateOrderReturnData = await returnOrderService.updateReturn(
        {
          ...commonFilter,
          _id: commonUtils.toObjectId(orderId),
        },
        {
          status: 'in-stock',
          wmCheckInDate: Date.now(),
          wmRemark,
        },
        session
      );

      if (!updateOrderReturnData) {
        await session.abortTransaction();
        session.endSession();
        return res.status(400).json(responseUtils.errorResponse(constantUtils.FAILED_TO_PROCESS));
      }
    }

    await session.commitTransaction();
    session.endSession();

    res.status(200).json(responseUtils.successResponse(constantUtils.EQUIPMENT_CHECKEDIN));
  } catch (error) {
    await session.abortTransaction();
    session.endSession();

    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get Equipment By QR Code
 *
 * @param {*} req
 * @param {*} res
 */
exports.getEquipmentByQRCode = async (req, res) => {
  try {
    let { qrCode, returnOrder } = req.query;

    if (!qrCode || !returnOrder || !mongoose.Types.ObjectId.isValid(returnOrder)) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.QRCODE_REQUIRED));
    }

    const response = await equipmentService.findQRCode(qrCode);

    if (response.length === 0) {
      return res.status(404).json(responseUtils.errorResponse(constantUtils.EQUIPMENT_NOT_FOUND));
    }

    // check QR code is outdated
    if (!(await equipmentService.checkQRCodeOutDated(response, qrCode)))
      return res.status(400).json(responseUtils.errorResponse(constantUtils.OUTDATED_QR_CODE));
    const returnOrderData = await returnOrderHistoryService.getSingleReturnOrderHistory({
      account: req.userData.account,
      equipment: commonUtils.toObjectId(response[0]._id),
      returnOrder: commonUtils.toObjectId(returnOrder),
      status: 'return',
      deletedAt: null,
    });

    if (!returnOrderData) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.RETURN_ORDER_NOT_FOUND));
    }

    const data = {
      _id: response[0]._id,
      name: response[0].name,
      image: response[0].equipmentImage,
      equipmentType: response[0].equipmentType,
      pmDispatchQuantity: returnOrderData.pmDispatchQuantity,
      comments: {
        wmComment: returnOrderData.wmComment,
        pmComment: [],
      },
    };

    res.status(200).json(responseUtils.successResponse(constantUtils.EQUIPMENT_CHECKEDIN, data));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.rejectPartialOrder = async (req, res) => {
  const session = await mongoose.startSession();
  const transactionOption = { ...transactionOptions };
  try {
    let { orderId } = req.params;
    let { remark } = req.body;

    session.startTransaction(transactionOption);
    const existManageOrder = await pmOrderManageEquipmentService.getPMOrderManageEquipmentById(
      orderId
    );

    if (!existManageOrder) {
      await session.abortTransaction();
      session.endSession();
      return res.status(400).json(responseUtils.errorResponse(constantUtils.ORDER_NOT_FOUND));
    }

    let orderStatus = {
      'partially-pre-transit': 'pre-transit',
      'partially-in-transit': 'in-transit',
      'partially-check-in': 'check-in',
      approved: 'rejected',
    };

    let updateManageOrder = await pmOrderManageEquipmentService.updatePMOrderManageEquipment(
      orderId,
      {
        status: orderStatus[existManageOrder.status],
        wmApprovedQuantity: existManageOrder.wmDispatchQuantity,
        remark: remark ?? null,
        updatedBy: req.userData._id,
        updatedAt: new Date(),
      },
      session
    );

    if (updateManageOrder) {
      const getManageData = await pmOrderManageEquipmentService.getPMOrderManageEquipment({
        pmOrder: updateManageOrder.pmOrder,
        status: existManageOrder.status,
        deletedAt: null,
      });

      if (getManageData.length === 0) {
        const getPMOrder = await pmOrderService.getPMOrderById(updateManageOrder.pmOrder);
        await pmOrderService.updatePMOrder(
          updateManageOrder.pmOrder,
          {
            status: orderStatus[getPMOrder.status],
            updatedBy: req.userData._id,
            updatedAt: new Date(),
          },
          session
        );
      }
    }

    await session.commitTransaction();
    session.endSession();
    res.status(200).json(responseUtils.successResponse(constantUtils.ORDER_REJECTED));
  } catch (error) {
    await session.abortTransaction();
    session.endSession();
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.getOrderPDFDetails = async (req, res) => {
  try {
    const { orderId } = req.params;

    let filter = {
      _id: commonUtils.toObjectId(orderId),
      account: req.userData.account,
      deletedAt: null,
    };

    let response = await warehouseOrderService.orderPDFDetails(filter);

    if (response.length === 0) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.MISSING_ORDER_DATA));
    }

    response = await this.currencyWiseTotalAmount(response[0], filter.account);
    // Generate PDF
    return await pdfTemplateService.exportOrderPDF(response, res);
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Calculate currency wise total
 *
 * @param {*} requestData
 * @param {*} account
 * @returns
 */
exports.currencyWiseTotalAmount = async (requestData, account) => {
  const getCurrencyUnit = await currencyUnitService.getCurrencyUnit({
    account,
  });

  let currencyTotal = [];
  for (let data of getCurrencyUnit) {
    let filterData = requestData.orderEquipment.filter(
      item => item.currencyUnitId.toString() === data._id.toString()
    );
    let totalQuantity = filterData.reduce((total, item) => total + item.totalQuantityValue, 0);
    if (totalQuantity > 0) {
      currencyTotal.push(`${data.symbol}${totalQuantity}`);
    }
  }
  requestData.currencyTotal = currencyTotal;
  return requestData;
};

/**
 * Get Linked Equipments
 *
 * @param {*} req
 * @param {*} res
 */
exports.getLinkedEquipments = async (req, res) => {
  try {
    const { orderId, equipmentTypeId } = req.params;
    let filter = {
      pmOrder: commonUtils.toObjectId(orderId),
      equipmentType: commonUtils.toObjectId(equipmentTypeId),
      account: req.userData.account,
      deletedAt: null,
    };

    let response = await pmOrderManageEquipmentService.getLinkedEquipments(filter);

    // check and add open quantity field
    for (let data of response) {
      data.openQuantity = data.wmApprovedQuantity - data.wmDispatchQuantity || 0;
    }

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.GET_PM_ORDER_EQUIPMENT_LIST, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Update Linked Equipments
 *
 * @param {*} req
 * @param {*} res
 */
exports.updateLinkedEquipments = async (req, res) => {
  try {
    let response;

    for (const element of req.body) {
      response = await equipmentOrderHistoryService.updateLinkedEquipments(
        {
          _id: commonUtils.toObjectId(element.equipmentOrderHistory),
          account: req.userData.account,
          deletedAt: null,
        },
        {
          wmDispatchQuantity: element.wmDispatchQuantity,
        }
      );

      const inventoryHistory = await inventoryHistoryService.getInventoryHistory(
        commonUtils.toObjectId(element.inventoryHistory)
      );

      const equipmentDifference = inventoryHistory.quantity - element.wmDispatchQuantity;
      let data;

      await inventoryHistoryService.updateInventoryHistory(
        commonUtils.toObjectId(element.inventoryHistory),
        {
          quantity: element.wmDispatchQuantity,
        }
      );

      const equipment = await equipmentService.getSingleEquipmentByFilter({
        _id: commonUtils.toObjectId(element.equipment),
        account: req.userData.account,
        deletedAt: null,
      });

      if (equipmentDifference < 0) {
        data = {
          quantity: equipment.quantity - Math.abs(equipmentDifference),
        };
      } else if (equipmentDifference > 0) {
        data = {
          quantity: equipment.quantity + equipmentDifference,
        };
      }

      await equipmentService.updateEquipment(commonUtils.toObjectId(element.equipment), data);
    }
    res
      .status(200)
      .json(
        responseUtils.successResponse(constantUtils.LINKED_EQUIPMENT_UPDATED_SUCCESSFULLY, response)
      );
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Remove Linked Equipments
 *
 * @param {*} req
 * @param {*} res
 */
exports.removeLinkedEquipment = async (req, res) => {
  try {
    const { equipmentId } = req.params;
    const { inventoryHistory, equipmentOrderHistory, pmOrderManageEquipment, quantity } = req.body;

    await equipmentOrderHistoryService.updateLinkedEquipments(
      { _id: equipmentOrderHistory },
      {
        deletedAt: new Date(),
        deletedBy: commonUtils.toObjectId(req.userData.id),
      }
    );

    // check inventory history data
    let getInventoryHistory = await inventoryHistoryService.getInventoryHistory(
      commonUtils.toObjectId(inventoryHistory)
    );

    if (getInventoryHistory) {
      // Get all inventory history data
      let getAllInvetoryHistory = await inventoryHistoryService.getInventoryHistoryDataByFilter(
        {
          equipment: getInventoryHistory.equipment,
          pmOrder: getInventoryHistory.pmOrder,
          account: req.userData.account,
          deletedAt: null,
        },
        -1
      );

      // update all inventory history data
      for (let data of getAllInvetoryHistory) {
        if (data.status !== 'linked') {
          break;
        }
        await inventoryHistoryService.updateInventoryHistory(data._id, {
          deletedAt: new Date(),
          deletedBy: commonUtils.toObjectId(req.userData.id),
        });
      }
    }

    let data = await pmOrderManageEquipmentService.searchPMOrderManageEquipment(
      commonUtils.toObjectId(pmOrderManageEquipment)
    );

    let getOrderHistory = await equipmentOrderHistoryService.getEquipmentHistory({
      equipment: equipmentId,
      pmOrderManageEquipment: pmOrderManageEquipment,
      account: req.userData.account,
      deletedAt: null,
    });

    data.equipment = getOrderHistory
      ? data.equipment
      : data.equipment.filter(eq => !eq.equals(equipmentId));

    await pmOrderManageEquipmentService.updatePMOrderManageEquipments(pmOrderManageEquipment, data);
    let equipment = await equipmentService.getSingleEquipmentByFilter(
      commonUtils.toObjectId(equipmentId)
    );

    await equipmentService.updateEquipment(commonUtils.toObjectId(equipmentId), {
      quantity: equipment.quantity + quantity,
    });

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.LINKED_EQUIPMENT_REMOVED_SUCCESSFULLY));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Remove Quantity From Open Order
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.removeQuantityFromOpenOrder = async (req, res) => {
  try {
    const { orderId } = req.params;
    const { pmOrderManageId, quantity, remark } = req.body;

    // Check if order id and pm order manage id is valid
    if (
      !mongoose.Types.ObjectId.isValid(orderId) ||
      !mongoose.Types.ObjectId.isValid(pmOrderManageId)
    ) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.INVALID_ID));
    }

    // Get manage order by id
    let getManageOrder = await pmOrderManageEquipmentService.getPMOrderManageEquipmentById(
      pmOrderManageId
    );

    // Remove requested quantity from approved quantity
    let updatedQuantiy = getManageOrder.wmApprovedQuantity - quantity;

    // Check and replace status if quantity is equal to wm dispatch quantity
    let manageStatus = getManageOrder.status;
    if (getManageOrder.wmDispatchQuantity == updatedQuantiy) {
      manageStatus = manageStatus.replace('partially-', '');
    }

    // Update manage order
    let updateManageOrder = await pmOrderManageEquipmentService.updatePMOrderManageEquipment(
      pmOrderManageId,
      {
        wmApprovedQuantity: updatedQuantiy,
        status: manageStatus,
        remark: remark ?? null,
      }
    );

    if (updateManageOrder) {
      // If wm approved quantity is 0 then update order status to rejected
      if (updateManageOrder.wmApprovedQuantity < 1) {
        await pmOrderManageEquipmentService.updatePMOrderManageEquipment(pmOrderManageId, {
          status: 'rejected',
        });
      }

      // Get manage order by pm order id
      let getManageData = await pmOrderManageEquipmentService.getPMOrderManageEquipment({
        pmOrder: orderId,
        account: req.userData.account,
        deletedAt: null,
      });

      let checkPartially = true;
      let counter = 0;
      for (let data of getManageData) {
        // check the partial order status
        if (data.status.includes('partially')) {
          checkPartially = false;
        }

        // check the rejected order status
        if (data.status === 'rejected') {
          counter++;
        }
      }

      // Check and update order status
      if (checkPartially) {
        let getPMOrder = await pmOrderService.getPMOrderById(orderId);
        let pmOrderStatus = getPMOrder.status;
        pmOrderStatus = pmOrderStatus.replace('partially-', '');

        await pmOrderService.updatePMOrder(orderId, { status: pmOrderStatus });
      }

      // Update pm order status to rejected if all manage order status is rejected
      if (counter === getManageData.length) {
        await pmOrderService.updatePMOrder(orderId, { status: 'rejected' });
      }
    }

    res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.UPDATE_PM_ORDER_DETAILS));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * get WM Equipment Counter
 *
 * @param {*} req
 * @param {*} res
 */
exports.getWmEquipmentCounter = async (req, res) => {
  try {
    const wmEquipmentCount = [
      { type: 'inventory', count: 0 },
      { type: 'register', count: false },
      { type: 'request', count: 0 },
      { type: 'openOrders', count: 0 },
      { type: 'checkIn', count: 0 },
      { type: 'checkOut', count: 0 },
    ];

    let filterData = {
      account: req.userData.account,
      deletedAt: null,
    };

    const inventory = await commonUtils.getCountFromQuery('equipment', filterData, {});
    wmEquipmentCount.find(item => item.type === 'inventory').count = inventory.allRecordsCount;

    if ('projectStatus' in req.query) {
      filterData = await commonUtils.filterProjectStatus(
        req.query.projectStatus,
        req.userData.account,
        filterData
      );
    }

    delete filterData?.projectStatus;

    if (
      req?.assignedProjectList &&
      !orderStatusArray.includes(req.query.status) &&
      ![global.constant.ADMIN_ROLE, global.constant.SUPER_ADMIN_ROLE].includes(
        req.userData.role.title
      )
    ) {
      filterData.project = { $in: req.assignedProjectList };
    }

    const requests = await wmOrderService.getPMOrderListCount(filterData, 'requested');

    wmEquipmentCount.find(item => item.type === 'request').count = requests.length;

    const checkouts = await wmOrderService.getPMOrderListCount(filterData, {
      $in: ['pre-transit', 'partially-pre-transit'],
    });

    wmEquipmentCount.find(item => item.type === 'checkOut').count = checkouts.length;

    const openOrders = await wmOrderService.getPMOrderListCount(filterData, {
      $in: ['approved', 'partially-pre-transit', 'partially-in-transit', 'partially-check-in'],
    });

    wmEquipmentCount.find(item => item.type === 'openOrders').count = openOrders.length;

    filterData.status = 'return';
    const returnOrders = await wmOrderService.warehouseCheckinList(filterData);

    wmEquipmentCount.find(item => item.type === 'checkIn').count = returnOrders.length;

    return res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.WAREHOUSE_COUNTER, wmEquipmentCount));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Add back to open order from check-out
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.addBackToOpenOrder = async (req, res) => {
  try {
    const { pmOrder } = req.params;
    const { pmOrderManageIds } = req.body;

    let isPmOrderExist = await pmOrderService.getPMOrderById(pmOrder);

    if (!isPmOrderExist) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.PM_ORDER_REQUEST_NOT_FOUND));
    }

    let isPmOrderManageEquipmentExist =
      await pmOrderManageEquipmentService.getPMOrderManageEquipment({
        _id: { $in: pmOrderManageIds },
        deletedAt: null,
      });

    let isEquipmentOrderHistoryExist =
      await equipmentOrderHistoryService.getEquipmentOrderHistoryByFilter({
        pmOrderManageEquipment: { $in: pmOrderManageIds },
        status: 'linked',
        account: req.userData.account,
        deletedAt: null,
      });

    if (isEquipmentOrderHistoryExist.length === 0) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.NO_EQUIPMENT_LINKED_IN_ORDER));
    }

    for (let pmOrderManageId of pmOrderManageIds) {
      let equipmentQuantities = {};

      let pmOrderManageEquipmentDoc = isPmOrderManageEquipmentExist.find(
        doc => doc._id.toString() === pmOrderManageId
      );

      if (!pmOrderManageEquipmentDoc) {
        continue;
      }

      // Iterate through isEquipmentOrderHistoryExist to accumulate wmDispatchQuantity for matching equipment
      for (let doc of isEquipmentOrderHistoryExist) {
        if (doc.pmOrderManageEquipment._id.toString() === pmOrderManageId) {
          for (let equipmentId of pmOrderManageEquipmentDoc.equipment) {
            if (equipmentId.toString() === doc.equipment._id.toString()) {
              if (!equipmentQuantities[equipmentId]) {
                equipmentQuantities[equipmentId] = {
                  totalWmDispatchQuantity: 0,
                  docToUpdate: null,
                };
              }
              equipmentQuantities[equipmentId].totalWmDispatchQuantity += doc.wmDispatchQuantity;
              if (!equipmentQuantities[equipmentId].docToUpdate) {
                equipmentQuantities[equipmentId].docToUpdate = doc;
              } else {
                // Mark subsequent matching documents as deleted
                await equipmentOrderHistoryService.updateEquipmentOrderHistory(doc._id, {
                  deletedAt: new Date(),
                  updatedBy: req.userData._id,
                  updatedAt: new Date(),
                });
              }
            }
          }
        }
      }

      // Update the first matching document with accumulated wmDispatchQuantity
      for (let equipmentId in equipmentQuantities) {
        let { totalWmDispatchQuantity, docToUpdate } = equipmentQuantities[equipmentId];
        if (docToUpdate) {
          await equipmentOrderHistoryService.updateEquipmentOrderHistory(docToUpdate._id, {
            wmDispatchQuantity: totalWmDispatchQuantity,
            updatedBy: req.userData._id,
            updatedAt: new Date(),
          });
        }
      }
    }

    // Update PM Order status if it is 'pre-transit'
    if (isPmOrderExist.status === 'pre-transit') {
      isPmOrderExist.status = 'approved';
    }

    await pmOrderService.updatePMOrder(pmOrder, {
      status: isPmOrderExist.status,
      updatedBy: req.userData._id,
      updatedAt: new Date(),
    });

    // Update PM Order Manage Equipment status to 'approved' and wmDispatchQuantity to 0
    await pmOrderManageEquipmentService.updatePMOrderManageEquipmentByPMOrderId(
      { _id: { $in: pmOrderManageIds }, deletedAt: null },
      { status: 'approved', wmDispatchQuantity: 0 }
    );

    // Update Equipment Order History status to 'pre-linked'
    await equipmentOrderHistoryService.updateEquipmentHistoryByFilter(
      {
        pmOrderManageEquipment: { $in: pmOrderManageIds },
        status: 'linked',
        account: req.userData.account,
        deletedAt: null,
      },
      {
        status: 'pre-linked',
        updatedBy: req.userData._id,
        updatedAt: new Date(),
      }
    );

    return res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.WM_REVERT_ORDER_TO_OPEN));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};
