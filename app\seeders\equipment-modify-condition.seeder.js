const Equipment = require('../models/equipment.model');

exports.up = async () => {
  try {
    const result = await Equipment.updateMany(
      { condition: 'maintenance' },
      { condition: 'quarantine' }
    );
    console.log(result.modifiedCount, 'Document updated');
  } catch (error) {
    console.error('Error ', error);
  }
};

exports.down = async () => {
  try {
    const result = await Equipment.updateMany(
      { condition: 'quarantine' },
      { condition: 'maintenance' }
    );
    console.log(result.modifiedCount, 'Document updated');
  } catch (error) {
    console.error('Error ', error);
  }
};
