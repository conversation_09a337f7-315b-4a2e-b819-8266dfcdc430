const mongoose = require('mongoose');

const InventoryHistory = new mongoose.Schema(
  {
    equipment: {
      type: mongoose.Types.ObjectId,
      ref: 'equipment',
    },
    status: {
      type: String,
      enum: [
        '',
        'in-stock',
        'linked',
        'pre-transit',
        'in-transit',
        'on-site',
        'return',
        'quarantine',
        'write off',
        'repaired',
        'partially-returned',
      ],
      default: '',
    },
    type: {
      type: String,
      enum: [
        '',
        'purchase',
        'cancel',
        'order',
        'order-received',
        'return',
        'return-received',
        'return-rejected',
        'quarantine',
        'maintenance',
        'repaired',
        'write-off',
      ],
      default: '',
    },
    orderNumber: {
      type: String,
      default: null,
    },
    quantity: {
      type: Number,
      default: 0,
    },
    inOut: {
      type: String,
      enum: ['', 'in', 'out'],
      default: '',
    },
    tracker: {
      type: String,
      default: null,
    },
    pmOrder: {
      type: mongoose.Types.ObjectId,
      ref: 'pm-order',
      default: null,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

InventoryHistory.index({ equipment: 1, deletedAt: 1, createdAt: -1 });
InventoryHistory.index({ account: 1, equipment: 1, deletedAt: 1 });
InventoryHistory.index({ pmOrder: 1, equipment: 1, deletedAt: 1 });

InventoryHistory.index({ account: 1, deletedAt: 1 });
InventoryHistory.index({ equipment: 1 });
InventoryHistory.index({ pmOrder: 1 });
InventoryHistory.index({ status: 1 });
InventoryHistory.index({ type: 1 });
InventoryHistory.index({ createdAt: -1 });
InventoryHistory.index({ updatedAt: -1 });

InventoryHistory.index({
  account: 1,
  deletedAt: 1,
  status: 1,
  type: 1,
});

module.exports = mongoose.model('inventory-history', InventoryHistory);
